# 服务列表排序功能实现说明

## 功能概述
在现有服务列表中新增排序功能，包括列表排序列显示和操作栏排序操作，与项目中已实现的排序功能保持一致的交互体验。

## 实现内容

### 1. 排序列显示
- ✅ 在服务列表最前面添加"排序"列
- ✅ 显示当前服务的排序序号（基于分页计算）
- ✅ 仅在选中分类时显示排序列

### 2. 操作栏排序功能
- ✅ 在每行服务的操作下拉菜单中新增"排序"操作项
- ✅ 仅在选中分类且服务已发布时显示排序选项
- ✅ 点击后弹出排序输入弹窗

### 3. 排序弹窗交互
- ✅ 使用 Modal.confirm 实现排序弹窗
- ✅ 使用 InputNumber 组件输入排序值
- ✅ 预填当前服务的排序位置
- ✅ 限制输入范围（1 到已发布服务总数）
- ✅ 显示提示信息：当前已发布服务总数

### 4. 业务规则实现
- ✅ **触发条件**：仅在选中某一分类时支持排序功能
- ✅ **权限限制**：仅对"已发布"状态的服务支持排序
- ✅ **排序逻辑**：支持输入数字进行排序，如果输入序号大于已发布服务总数，自动排到最后
- ✅ **操作后状态**：排序成功后停留在当前页面，保持分页器状态

### 5. 技术实现
- ✅ 使用现有的 `sortGlobalService` 接口
- ✅ 接口参数：`{ id: record.id, sort_flag: 2, number: position }`
- ✅ 权限控制：使用 `web_link:order` 权限
- ✅ 样式实现：添加 `.sort-tip` 样式类

## 代码修改文件

### 1. src/views/services/appServiceList.tsx
- 添加 InputNumber 组件导入
- 新增 handleSort 方法实现排序弹窗逻辑
- 修改 getDropdown 方法，添加排序操作项
- 修改 getColumns 方法，添加排序列显示逻辑
- 根据筛选条件动态显示不同的列配置

### 2. src/assets/index.scss
- 添加 `.sort-tip` 样式类，用于排序弹窗提示文本

## 功能特点

### 1. 条件显示
- 排序列：仅在选中分类时显示
- 排序操作：仅在选中分类且服务已发布时显示

### 2. 用户体验
- 弹窗预填当前位置，方便用户参考
- 显示已发布服务总数，帮助用户了解排序范围
- 输入验证，确保排序值有效
- 操作成功后保持当前页面状态

### 3. 权限控制
- 使用现有权限系统 `requirePerm`
- 权限标识：`web_link:order`

### 4. 接口调用
- 使用现有 API：`api.sortGlobalService`
- 参数格式：
  ```javascript
  {
    id: record.id,        // 服务ID
    sort_flag: 2,         // 指定位置排序
    number: position      // 目标位置
  }
  ```

## 测试建议

### 1. 功能测试
- [ ] 验证排序列仅在选中分类时显示
- [ ] 验证排序操作仅对已发布服务显示
- [ ] 验证排序弹窗正常弹出和关闭
- [ ] 验证排序值输入和验证逻辑
- [ ] 验证排序操作成功后页面状态保持

### 2. 边界测试
- [ ] 输入超出范围的排序值
- [ ] 对未发布服务尝试排序
- [ ] 在未选中分类时检查功能隐藏
- [ ] 权限不足时的功能隐藏

### 3. 兼容性测试
- [ ] 与现有排序功能的交互
- [ ] 分页器状态保持
- [ ] 筛选条件变更时的列显示切换

## 注意事项

1. **权限依赖**：功能依赖 `web_link:order` 权限，确保用户具有相应权限
2. **接口依赖**：依赖 `sortGlobalService` 接口，确保接口正常可用
3. **状态管理**：排序后会重新获取数据，保持当前分页状态
4. **样式依赖**：新增样式类 `.sort-tip`，确保样式文件正确加载

## 后续优化建议

1. 可考虑添加拖拽排序功能
2. 可考虑批量排序功能
3. 可考虑排序历史记录功能
4. 可考虑排序操作的撤销功能
