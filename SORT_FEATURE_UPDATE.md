# 服务列表排序功能更新说明

## 功能概述
根据要求，已将服务列表的排序列功能从显示序号改为上下箭头按钮，参考 `src/views/find/filmTvMgr.tsx` 的实现方式。

## 更新内容

### 1. 排序列显示（上下箭头）
- ✅ 使用 `OrderColumn` 组件替代序号显示
- ✅ 显示上移/下移箭头按钮
- ✅ 仅在选中分类时显示排序列
- ✅ 仅对已发布服务启用排序功能

### 2. 保留操作栏排序功能
- ✅ 操作下拉菜单中的"排序"选项保持不变
- ✅ 支持输入数字指定具体位置
- ✅ 与上下箭头功能并存

## 技术实现

### 1. 导入 OrderColumn 组件
```typescript
import { A, Drawer, OrderColumn, Table } from '@components/common';
```

### 2. 修改排序列实现
```typescript
const sortColumns: any = this.state.category ? [
  {
    title: '排序',
    key: 'sort_order',
    render: (_: any, record: any, i: number) => {
      const currentSeq = getSeq(i);
      const isDisabled = !record.enabled; // 仅对未发布服务禁用
      const enabledCount = this.props.tableList.records.filter((item: any) => item.enabled).length;
      
      return (
        <OrderColumn
          pos={currentSeq}
          start={1}
          end={enabledCount}
          perm="web_link:order"
          disable={isDisabled}
          onUp={() => this.orderUpDown(record, currentSeq, -1)}
          onDown={() => this.orderUpDown(record, currentSeq, 1)}
        />
      );
    },
    width: 80,
  },
] : [];
```

### 3. 新增上下移动方法
```typescript
orderUpDown = (record: any, _: number, type: number) => {
  setLoading(this, true);
  api
    .sortGlobalService({
      id: record.id,
      sort_flag: type === -1 ? 0 : 1, // 0: 向上, 1: 向下
    })
    .then(() => {
      message.success('操作成功');
      this.getData(); // 保持当前页面状态
      setLoading(this, false);
    })
    .catch(() => {
      setLoading(this, false);
    });
};
```

## 功能特点

### 1. 双重排序方式
- **上下箭头**：快速上移/下移一位，适合微调
- **输入数字**：精确指定位置，适合大幅调整

### 2. 业务规则保持
- 仅在选中分类时显示排序列
- 仅对已发布服务启用排序功能
- 权限控制：`web_link:order`

### 3. 用户体验
- 直观的上下箭头操作
- 边界限制（第一项禁用上移，最后一项禁用下移）
- 操作后保持当前页面状态

## 接口调用

### 1. 上下移动
```typescript
api.sortGlobalService({
  id: record.id,
  sort_flag: type === -1 ? 0 : 1, // 0: 向上, 1: 向下
})
```

### 2. 指定位置（操作栏）
```typescript
api.sortGlobalService({
  id: record.id,
  sort_flag: 2, // 指定位置
  number: position,
})
```

## 测试要点

1. **排序列显示**：仅在选中分类时显示
2. **权限控制**：需要 `web_link:order` 权限
3. **状态限制**：仅对已发布服务启用
4. **边界处理**：第一项/最后一项的按钮禁用
5. **功能并存**：上下箭头与操作栏排序同时可用

## 参考实现
完全参考 `src/views/find/filmTvMgr.tsx` 中的排序列实现：
- 使用相同的 `OrderColumn` 组件
- 相同的权限控制逻辑
- 相同的禁用条件判断
- 相同的接口调用方式

更新完成！现在服务列表具有与项目其他模块一致的排序交互体验。
