body,
html,
#root,
.router-div {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px !important;
  min-width: 1366px;
}

.sort-up,
.sort-down {
  font-size: 14px;
}

.sort-up {
  color: #3399cc;
}

.sort-down {
  color: #33cccc;
}

.sort-tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.select-multi-item {
  white-space: pre-wrap;
}

.list-pic {
  max-width: 100px;
  max-height: 60px;
}

.comment-img {
  max-width: 100px;
  object-fit: cover;
}

.line-max {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-max-8 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 8; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.line-max-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.line-max-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.pagination-pages {
  text-align: right;
}

.ant-confirm-title p {
  margin-left: 42px;
  word-wrap: break-word;
  word-break: break-all;
}

.ant-confirm-title,
.ant-confirm-content {
  word-break: break-all;
}

.title-right-col {
  text-align: right;
}

.timeline-dot,
.timeline-dot-big {
  margin-left: 106px !important;
}

.timeline-dot,
.timeline-dot-big {
  margin-left: 106px !important;
}

.timeline-dot-big:before,
.timeline-dot:before {
  margin-left: -106px;
  margin-top: -6px;
  content: attr(data-show);
  text-align: right;
  width: 100px;
  position: absolute;
}

.timeline-dot-edit::before {
  color: red;
}

.timeline-dot-edit::after {
  margin-left: -100px;
  margin-top: -30px;
  content: '改';
  text-align: right;
  width: 20px;
  text-align: center;
  position: absolute;
  background-color: red;
  color: white;
}

.ant-timeline-item-last::after {
  margin-top: -53px;
}

.timeline-dot-big:before {
  font-size: 16px;
}


.component-content {
  margin-top: 12px;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  height: 100%;
  padding: 24px;
}

.layout-breadcrumb {
  text-align: right;
  margin-top: 6px;
}

.a-disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.ant-table-tbody>tr>td {
  padding: 12px 8px !important;
  word-break: break-all;
}

.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
  z-index: 9999;
}

.row-dragging td {
  padding: 12px 8px !important;
}

.row-dragging .drag-visible {
  visibility: visible;
}

.row-dragging .drag-invisible {
  visibility: hidden;
}

.ant-table-thead>tr>th {
  padding: 16px 8px !important;
  word-break: break-all;
}

.rox-drawer {

  .spin-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.25);
    z-index: 9;

    .ant-spin {
      position: absolute;
      top: 50%;
      /* 垂直居中 */
      left: 50%;
      /* 水平居中 */
      transform: translate(-50%, -50%);
    }

    // right: 0;
    // bottom: 0;
    // top: 50%;
    /* 垂直居中 */
    // left: 50%;
    /* 水平居中 */
    // transform: translate(-50%, -50%);
  }

  .ant-drawer-wrapper-body {
    overflow: hidden !important;
    display: flex;
    flex-direction: column;

    .ant-drawer-body {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;

      .rox-drawer-content {
        flex: 1;
        overflow: auto;
      }

      .rox-drawer-footer {
        position: relative;
        border-top: 1px solid #e9e9e9;
        text-align: right;
        padding-top: 16px;
      }
    }
  }
}

.article-preview {
  img {
    width: 100% !important;
  }
}

.creator-platform-content img {
  max-width: 100%;
}

.wg img {
  max-width: 100%;
}

.ant-spin-blur {
  pointer-events: initial !important;
}

.start-page-datepicker .ant-calendar-footer-extra {
  float: left;
}

.previewMCN .ql-photo-desc {
  padding: 0 40px;
  font-size: 12px;
  color: #aaa;
  text-align: center;
}

.ccr_more_jump .ant-form-explain {
  margin-left: 155px;
}

.wrapper_drawer_dialog {
  height: 100%;

  .ant-spin-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}