/* eslint-disable import/prefer-default-export */
import { RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';

export const serviceApi = {
  // 服务头图管理
  getServiceFocusList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('web_link_top/list', body, request),
  getServiceFocusOnCount: () => fetch.get('web_link_top/count'),
  updateServiceFocusStatus: (type: string, body: RequestBody) =>
    fetch.post(`web_link_top/${type}`, body),
  deleteServiceFocus: (body: RequestBody) => fetch.post('web_link_top/delete', body),
  sortServiceFocus: (body: RequestBody) => fetch.post('web_link_top/order', body),
  updateServiceFocus: (body: RequestBody) => fetch.post('web_link_top/update', body),
  createServiceFocus: (body: RequestBody) => fetch.post('web_link_top/create', body),

  // 服务分类
  getServiceCategoryList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('web_link_category/list', body, request),
  sortServiceCategory: (body: RequestBody) => fetch.post('web_link_category/order', body),
  deleteServiceCategory: (body: RequestBody) => fetch.post('web_link_category/delete', body),
  updateServiceCategory: (body: RequestBody) => fetch.post('web_link_category/update', body),
  createServiceCategory: (body: RequestBody) => fetch.post('web_link_category/create', body),
  // 服务头部管理
  getServiceHeader: (body: RequestBody) => fetch.get('web_link/head_query', body),
  saveServiceHeader: (body: RequestBody) => fetch.json('web_link/head_save', body),

  // 服务推荐管理
  getServiceRecommend: (body: RequestBody) => fetch.get('web_link/recommend_query', body),
  saveServiceRecommend: (body: RequestBody) => fetch.json('web_link/recommend_save', body),

  // 服务通用
  getSimpleCategoryList: () => fetch.get('web_link_category/simple_list'),
  getServiceDetail: (body: RequestBody) => fetch.get('web_link/detail', body),
  getServiceAreaList: () => fetch.get('area/list_area_tree', { belong: 2 }),

  // 全局服务
  getAppServiceList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('web_link/list', body, request),
  sortAppService: (body: RequestBody) => fetch.post('web_link/global_order', body),
  updateAppServiceRecommend: (type: string, body: RequestBody) =>
    fetch.post(`web_link/${type}`, body),
  updateAppServiceStatus: (type: string, body: RequestBody) =>
    fetch.post(`web_link/global_${type}`, body),
  deleteAppService: (body: RequestBody) => fetch.post('web_link/global_delete', body),
  updateAppService: (body: RequestBody) => fetch.post('web_link/update', body),
  createAppService: (body: RequestBody) => fetch.post('web_link/create', body),
  getCommonNameServiceDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('common_name_recommend/detail', body, request),
  saveCommonNameService: (body: RequestBody) => fetch.post('common_name_recommend/save', body),
  //  /endpoint/
  // /endpoint/common_name_recommend/save

  // 搜索关键词
  getSearchKeywordList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('web_link_relevance/list', body, request),
  createSearchKeyword: (body: RequestBody) => fetch.post('web_link_relevance/create', body),
  deleteSearchKeyword: (body: RequestBody) => fetch.post('web_link_relevance/delete', body),
  updateSearchKeyword: (body: RequestBody) => fetch.post('web_link_relevance/update', body),
  getSearchKeywordCategoryList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('web_link_relevance_class/list', body, request),
  createSearchKeywordCategory: (body: RequestBody) =>
    fetch.post('web_link_relevance_class/create', body),
  deleteSearchKeywordCategory: (body: RequestBody) =>
    fetch.post('web_link_relevance_class/delete', body),
  updateSearchKeywordCategory: (body: RequestBody) =>
    fetch.post('web_link_relevance_class/update', body),
  getAllKeywordList: () => fetch.get('web_link_relevance_class/simple_list', {}),

  // 推荐服务
  getServiceRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('service_recommend/list', body, request),
  updateSortServiceRecommend: (body: RequestBody) =>
    fetch.post('service_recommend/update_sort', body),
  updateStatusServiceRecommend: (body: RequestBody) =>
    fetch.post('service_recommend/update_status', body),
  deleteServiceRecommend: (body: RequestBody) => fetch.post('service_recommend/delete', body),
  getListAllRecommend: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('service_recommend/list_all_servcie', body, request),
  createServiceRecommend: (body: RequestBody) => fetch.post('service_recommend/create', body),
  updateServiceRecommend: (body: RequestBody) => fetch.post('service_recommend/update', body),
  getServiceRecommendDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('service_recommend/detail', body, request),
  getListServiceRecommend: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('service_recommend/list_servcie_recomemnd', body, request),
  getServiceRecommendVipDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('service_recommend/vip_detail', body, request),
  vipSaveServiceRecommend: (body: RequestBody) => fetch.post('service_recommend/vip_save', body),
  // 全局服务管理排序
  sortGlobalService: (body: RequestBody) => fetch.post('web_link/order', body),
};
